# AnZhiYu 主题低难度优化项目实施指南

## 概述

本指南包含8个低难度优化项目，旨在快速提升博客的用户体验和功能。每个项目都提供了详细的操作步骤和验证方法。

## 优化项目清单

-
  1. [x] 字体优化
-
  2. [x] 页面过渡动画 (Pjax)
-
  3. [x] 推荐文章展示
-
  4. [x] 分类层级优化
-
  5. [x] 静态资源 CDN
-
  6. [x] 插件配置优化
-
  7. [x] 文章封面图片
-
  8. [x] 文章摘要优化

---

## 1. 字体优化

### 目标说明

优化中文字体显示效果，提升阅读体验，使用更适合中文阅读的字体栈。

### 操作步骤

**步骤1：修改主题字体配置** 打开文件：`_config.anzhiyu.yml`

找到字体相关配置（如果没有则添加）：

```yaml
# 字体设置
font:
  enable: true
  # 全局字体
  global-font-size: 
  code-font-size:
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Lato, Roboto, "PingFang SC", "Microsoft YaHei", sans-serif
  code-font-family: 'JetBrains Mono', 'Cascadia Code', Menlo, Monaco, consolas, 'courier new', monospace
```

**步骤2：如果主题不支持字体配置，直接修改CSS**
打开文件：`themes/anzhiyu/source/css/_global/index.styl`

添加或修改以下内容：

```stylus
// 优化中文字体
body
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Lato, Roboto, "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", "Microsoft Sans Serif", sans-serif

// 代码字体优化
code, pre, .highlight
  font-family: 'JetBrains Mono', 'Cascadia Code', 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', 'Source Code Pro', Menlo, Consolas, 'courier new', monospace
```

### 验证方法

```bash
hexo clean && hexo g && hexo s
```

访问
`http://localhost:4000`，检查页面字体是否变得更加清晰易读，特别是中文内容的显示效果。

---

## 2. 页面过渡动画 (Pjax)

### 目标说明

启用 Pjax 功能实现无刷新页面切换，提升用户体验的流畅性。

### 操作步骤

**步骤1：在主题配置中启用 Pjax** 打开文件：`_config.anzhiyu.yml`

找到 Pjax 相关配置并启用：

```yaml
# Pjax 无刷新加载
pjax:
  enable: true
  exclude:
# - /music/
# - /no-pjax/
```

**步骤2：配置加载动画** 在同一文件中找到加载动画配置：

```yaml
# 加载动画
loading:
  enable: true
  # 加载动画类型
  # 1: 默认加载动画
  # 2: 自定义加载动画
  type: 1
```

**步骤3：配置页面切换动画**

```yaml
# 页面动画
page_animation:
  enable: true
  # 动画类型: fade, slide, zoom
  type: fade
  # 动画持续时间 (ms)
  duration: 300
```

### 验证方法

```bash
hexo clean && hexo g && hexo s
```

访问网站后，点击不同页面链接，观察页面是否无刷新切换，并且有平滑的过渡动画效果。

---

## 3. 推荐文章展示

### 目标说明

配置推荐文章功能，突出重要内容，提升内容曝光度。

### 操作步骤

**步骤1：在主题配置中启用推荐文章** 打开文件：`_config.anzhiyu.yml`

找到或添加推荐文章配置：

```yaml
# 推荐文章
recommend:
  enable: true
  # 推荐文章数量
  limit: 6
  # 推荐文章标题
  title: 推荐文章
  # 推荐文章排序方式: date, updated, random
  order_by: date
```

**步骤2：标记推荐文章** 在需要推荐的文章的 Front Matter 中添加 `sticky` 属性：

打开任意一篇文章，例如：`source/_posts/example-post.md`

```yaml
---
title: 示例文章标题
date: 2024-01-01 12:00:00
categories: 技术
tags: 
  - Hexo
  - 博客
sticky: 100  # 数值越大，优先级越高
---
```

**步骤3：配置首页推荐文章显示** 在 `_config.anzhiyu.yml`
中确保首页显示配置正确：

```yaml
# 首页设置
index:
  # 首页推荐文章
  top_post:
    enable: true
    # 推荐文章数量
    limit: 3
```

### 验证方法

```bash
hexo clean && hexo g && hexo s
```

访问首页，检查是否显示了推荐文章区域，并且设置了 `sticky`
属性的文章是否出现在推荐位置。

---

## 4. 分类层级优化

### 目标说明

重新规划文章分类结构，确保分类层次清晰，提升内容的可发现性。

### 操作步骤

**步骤1：规划分类结构** 建议的分类结构：

```
技术分享/
├── 前端开发
├── 后端开发
├── 工具使用
└── 学习笔记

生活随笔/
├── 日常生活
├── 读书笔记
└── 旅行记录

项目展示/
├── 个人项目
└── 开源贡献
```

**步骤2：配置分类页面** 打开文件：`_config.anzhiyu.yml`

确保分类页面配置正确：

```yaml
# 分类页面
category:
  enable: true
  # 分类页面布局
  layout: category
  # 是否显示文章数量
  show_count: true
  # 分类排序方式: name, length
  order_by: name
```

**步骤3：批量更新现有文章分类** 检查 `source/_posts/`
目录下的所有文章，统一分类格式：

```yaml
---
title: 文章标题
date: 2024-01-01
categories: 
  - 技术分享
  - 前端开发  # 子分类
tags:
  - JavaScript
  - Vue.js
---
```

**步骤4：创建分类页面** 如果还没有分类页面，创建：

```bash
hexo new page categories
```

编辑 `source/categories/index.md`：

```yaml
---
title: 分类
date: 2024-01-01
type: categories
layout: categories
---
```

### 验证方法

```bash
hexo clean && hexo g && hexo s
```

访问分类页面，检查分类结构是否清晰，文章是否正确归类。

---

## 5. 静态资源 CDN

### 目标说明

配置 CDN 加速静态资源加载，提升全球访问速度。

### 操作步骤

**步骤1：配置 CDN 设置** 打开文件：`_config.anzhiyu.yml`

找到或添加 CDN 配置：

```yaml
# CDN 设置
CDN:
  # 是否启用 CDN
  enable: true
  # CDN 提供商
  provider: jsdelivr # 可选: jsdelivr, unpkg, cdnjs

  # 第三方库 CDN
  third_party:
    jquery: https://cdn.jsdelivr.net/npm/jquery@latest/dist/jquery.min.js
    vue: https://cdn.jsdelivr.net/npm/vue@next/dist/vue.global.prod.js

  # 主题资源 CDN
  theme:
    css: https://cdn.jsdelivr.net/npm/hexo-theme-anzhiyu@latest/source/css/
    js: https://cdn.jsdelivr.net/npm/hexo-theme-anzhiyu@latest/source/js/
```

**步骤2：配置图片 CDN（可选）** 如果有图床服务，可以配置：

```yaml
# 图片 CDN
image_cdn:
  enable: false
  # 图床地址
  url: https://your-image-cdn.com/
```

**步骤3：配置字体 CDN**

```yaml
# 字体 CDN
font_cdn:
  enable: true
  google_fonts: https://fonts.googleapis.com/
  # 或使用国内镜像
  # google_fonts: https://fonts.font.im/
```

### 验证方法

```bash
hexo clean && hexo g && hexo s
```

打开浏览器开发者工具，检查 Network 标签页，确认静态资源是否从 CDN 加载。

---

## 6. 插件配置优化

### 目标说明

检查并优化所有插件配置，移除不必要的插件，提升网站性能。

### 操作步骤

**步骤1：检查当前安装的插件**

```bash
npm list --depth=0
```

**步骤2：优化 hexo-neat 配置** 打开文件：`_config.yml`

找到或添加 hexo-neat 配置：

```yaml
# hexo-neat 压缩优化
neat_enable: true
neat_html:
  enable: true
  exclude:
neat_css:
  enable: true
  exclude:
    - "*.min.css"
neat_js:
  enable: true
  mangle: true
  output:
  compress:
  exclude:
    - "*.min.js"
```

**步骤3：优化搜索插件配置**

```yaml
# hexo-generator-search 配置
search:
  path: search.xml
  field: post
  content: true
  format: html
```

**步骤4：优化 Feed 插件配置**

```yaml
# hexo-generator-feed 配置
feed:
  enable: true
  type: atom
  path: atom.xml
  limit: 20
  hub:
  content:
  content_limit: 140
  content_limit_delim: " "
  order_by: -date
  icon: icon.png
  autodiscovery: true
  template:
```

**步骤5：移除不必要的插件** 检查以下插件是否真正需要，如不需要可以卸载：

```bash
# 示例：如果不需要某个插件
npm uninstall hexo-plugin-name
```

### 验证方法

```bash
hexo clean && hexo g && hexo s
```

检查生成速度是否提升，网站功能是否正常。

---

## 7. 文章封面图片

### 目标说明

为所有文章添加高质量封面图片，统一图片尺寸和风格，提升视觉效果。

### 操作步骤

**步骤1：准备封面图片**

- 建议尺寸：1200x630px（适合社交媒体分享）
- 格式：WebP 或 JPG
- 存放位置：`source/images/covers/`

**步骤2：配置默认封面** 打开文件：`_config.anzhiyu.yml`

```yaml
# 默认封面设置
default_cover:
  enable: true
  # 默认封面图片列表
  covers:
    - /images/covers/default-1.webp
    - /images/covers/default-2.webp
    - /images/covers/default-3.webp
    - /images/covers/default-4.webp
    - /images/covers/default-5.webp
```

**步骤3：为文章添加封面** 编辑文章的 Front Matter：

```yaml
---
title: 文章标题
date: 2024-01-01
categories: 技术分享
tags:
  - Hexo
cover: /images/covers/article-cover.webp  # 添加封面图片
---
```

**步骤4：批量处理现有文章** 创建一个简单的脚本来批量添加封面（可选）：

```javascript
// 在 scripts/ 目录下创建 add-covers.js
const fs = require("fs");
const path = require("path");

// 默认封面列表
const defaultCovers = [
  "/images/covers/default-1.webp",
  "/images/covers/default-2.webp",
  "/images/covers/default-3.webp",
];

// 这里可以添加批量处理逻辑
```

### 验证方法

```bash
hexo clean && hexo g && hexo s
```

访问首页和文章页面，检查封面图片是否正确显示，样式是否统一。

---

## 8. 文章摘要优化

### 目标说明

为每篇文章添加精心编写的摘要，确保摘要能够吸引读者点击。

### 操作步骤

**步骤1：配置摘要显示** 打开文件：`_config.anzhiyu.yml`

```yaml
# 摘要设置
excerpt:
  enable: true
  # 摘要长度
  length: 150
  # 摘要结尾
  more: " ..."
  # 是否显示阅读更多按钮
  read_more: true
```

**步骤2：为文章添加自定义摘要** 在文章中使用 `<!-- more -->` 标签或在 Front
Matter 中添加 `excerpt`：

方法一：使用 more 标签

```markdown
---
title: 文章标题
date: 2024-01-01
---

这里是文章的摘要内容，会显示在首页列表中。这部分内容应该简洁明了，能够吸引读者点击阅读全文。

<!-- more -->

这里是文章的正文内容...
```

方法二：使用 Front Matter

```yaml
---
title: 文章标题
date: 2024-01-01
excerpt: 这里是自定义的文章摘要，会覆盖自动生成的摘要。应该简洁明了，突出文章的核心内容和价值。
---

文章正文内容...
```

**步骤3：优化摘要质量** 摘要编写建议：

- 长度控制在 100-150 字
- 突出文章的核心价值
- 使用吸引人的语言
- 避免技术术语过多
- 可以提出问题引起读者兴趣

**步骤4：配置 SEO 相关摘要**

```yaml
# SEO 设置
seo:
  # 自动生成描述
  auto_description: true
  # 描述长度
  description_length: 150
```

### 验证方法

```bash
hexo clean && hexo g && hexo s
```

访问首页，检查文章列表中的摘要显示是否正确，内容是否吸引人。同时检查文章页面的
meta description 是否正确设置。

---

## 完成后的整体验证

完成所有优化后，进行全面测试：

```bash
hexo clean && hexo g && hexo s
```

### 检查项目清单：

1. [ ] 字体显示是否更加清晰
2. [ ] 页面切换是否流畅无刷新
3. [ ] 推荐文章是否正确显示
4. [ ] 分类结构是否清晰
5. [ ] 静态资源是否从 CDN 加载
6. [ ] 网站生成速度是否提升
7. [ ] 文章封面是否统一美观
8. [ ] 文章摘要是否吸引人

### 性能测试建议：

- 使用 Google PageSpeed Insights 测试页面性能
- 检查浏览器开发者工具中的 Network 面板
- 测试移动端和桌面端的显示效果
- 验证所有链接和功能是否正常工作

### 故障排除：

如果遇到问题，请检查：

1. 配置文件语法是否正确（YAML 格式）
2. 文件路径是否正确
3. 插件是否正确安装
4. 浏览器缓存是否需要清理

---

## 结语

如果所有项目都正常工作，说明低难度优化项目已经成功完成！这些优化将显著提升您博客的用户体验和性能。

完成这些基础优化后，您可以考虑进行中等难度和高难度的优化项目，进一步提升博客的功能和视觉效果。

**创建时间：** 2025年1月26日 **适用主题：** AnZhiYu **Hexo 版本：** 7.3.0+
