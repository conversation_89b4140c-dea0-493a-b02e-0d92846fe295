if theme.aside.card_author.enable
  .card-widget.card-info
    .card-content
      .card-info-avatar.is-center
        .top-group
          if theme.aside.card_author.sayhi
            .sayhi#sayhi(onclick="anzhiyu.changeSayHelloText()")= theme.aside.card_author.sayhi
      .avatar
        img(alt="avatar", src=url_for(theme.avatar.img) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'`)
        if theme.author_status.enable && theme.author_status.statusImg
          .sticker
            img.sticker-img(src=url_for(theme.author_status.statusImg), alt="status")
      .description!= theme.aside.card_author.content || config.description
      .bottom-group
        span.left
          .name= config.author
          .desc!= theme.aside.card_author.description || config.subtitle
        .social-icons.is-center
          if theme.social
            each value, label in theme.social
              - var array = value.split('||')
              a.social-icon(href=url_for(trim(array[0])), title=label)
                i(class=trim(array[1]))
