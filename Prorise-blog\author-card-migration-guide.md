# 作者卡片迁移指南：从 Solitude 到 AnZhiYu

## 📋 概述

本指南将帮助您将 Solitude 主题的现代化作者卡片样式迁移到 AnZhiYu 主题中，包括悬停效果、渐变背景和统计信息展示。

## 🎯 目标效果

- ✅ 渐变背景的作者卡片
- ✅ 悬停显示描述文字效果
- ✅ 现代化的头像和贴纸设计
- ✅ 优雅的社交图标布局
- ✅ 统一的统计信息展示

## 📁 涉及文件

### 需要修改的文件：
1. `themes/anzhiyu/layout/includes/widget/card_author.pug`
2. `themes/anzhiyu/source/css/_layout/aside.styl`
3. `_config.anzhiyu.yml`

### 参考文件：
1. `hexo-theme-solitude-dev/layout/includes/widgets/aside/asideInfoCard.pug`
2. `hexo-theme-solitude-dev/source/css/_layout/aside.styl`
3. `hexo-theme-solitude-dev/_config.yml`

---

## 🔧 步骤一：备份原始文件

在开始修改前，请先备份原始文件：

```bash
# 进入博客根目录
cd Prorise-blog

# 创建备份目录
mkdir -p backup/author-card-migration

# 备份原始文件
cp themes/anzhiyu/layout/includes/widget/card_author.pug backup/author-card-migration/
cp themes/anzhiyu/source/css/_layout/aside.styl backup/author-card-migration/
cp _config.anzhiyu.yml backup/author-card-migration/
```

---

## 🎨 步骤二：修改 Pug 模板文件

### 文件：`themes/anzhiyu/layout/includes/widget/card_author.pug`

**原始内容：**
```pug
if theme.aside.card_author.enable
  .card-widget.card-info
    .card-content
      if theme.author_status.enable
        .author-info__sayhi#author-info__sayhi(onclick="anzhiyu.changeSayHelloText()")
      .author-info-avatar
        img.avatar-img(src=url_for(theme.avatar.img) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt="avatar")
        if theme.author_status.enable && theme.author_status.statusImg
          .author-status
            img(src=url_for(theme.author_status.statusImg) class='g-status' alt="status")

      .author-info__description!= theme.aside.card_author.description || config.description

      if(theme.social)
        .author-info__bottom-group
          a.author-info__bottom-group-left(href=url_for(theme.aside.card_author.name_link))
            h1.author-info__name=config.author
            .author-info__desc=config.subtitle
          .card-info-social-icons.is-center
            !=fragment_cache('social', function(){return partial('includes/header/social')}) 
```

**替换为（Solitude 风格）：**
```pug
if theme.aside.card_author.enable
  .card-widget.card-info
    .card-content
      .card-info-avatar.is-center
        .top-group
          if theme.author_status.enable
            .sayhi#sayhi(onclick="anzhiyu.changeSayHelloText()")= theme.aside.card_author.sayhi || "你好！"
      .avatar
        img(alt="avatar", src=url_for(theme.avatar.img) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'`)
        if theme.author_status.enable && theme.author_status.statusImg
          .sticker
            img.sticker-img(src=url_for(theme.author_status.statusImg), alt="status")
      .description!= theme.aside.card_author.description || config.description
      .bottom-group
        span.left
          .name= config.author
          .desc!= config.subtitle || theme.aside.card_author.subtitle
        .social-icons.is-center
          if theme.social
            each value, label in theme.social
              - var array = value.split('||')
              a.social-icon(href=url_for(trim(array[0])), title=label)
                i(class=trim(array[1]))
```

---

## 🎨 步骤三：修改 CSS 样式文件

### 文件：`themes/anzhiyu/source/css/_layout/aside.styl`

需要在文件中找到 `.card-info` 相关样式并替换。

**查找位置：** 大约在第 85-200 行之间

**原始样式：** 
```stylus
.author-info-avatar 
  user-select: none
  img
    filter: blur(0) brightness(1);
  .author-status
    position: absolute;
    bottom: 2px;
    right 2px
    width: 33px;
    height: 33px;
    border-radius: 2em;
    background-color: var(--anzhiyu-white);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: .3s .2s;
    transform: scale(1);
    img
      width: 26px;
      height: 26px;
      border-radius: 0px;
      transition: 0s
```

**替换为（Solitude 风格）：**
```stylus
.card-info
  position relative
  &::before
    background linear-gradient(-25deg, var(--anzhiyu-main), var(--anzhiyu-main))
    position absolute
    width 100%
    height 100%
    left 0
    top 0
    content ''

  &:hover
    .avatar, .sticker
      transform scale(0)

    .description
      opacity 1

  .sayhi
    width fit-content
    font-size 12px
    background var(--anzhiyu-white-op)
    border-radius 8px
    cursor pointer
    min-width 100px
    padding 2px 10px
    color var(--anzhiyu-white)
    transition all .3s

    &:hover
      background var(--anzhiyu-white)
      color var(--anzhiyu-main)
      transform scale(1.1)
      transition all .3s
      
  .avatar
    width 118px
    height 118px
    min-width 118px
    min-height 118px
    right calc(50% - 59px)
    top 90px
    position absolute
    transition cubic-bezier(.69,.39,0,1.21) .3s
    transform-origin bottom

    img
      border-radius 50%
      width 100%
      height 100%
      border 5px solid var(--anzhiyu-card-bg)
      overflow hidden

    .sticker
      position absolute
      bottom 2px
      right 2px
      width 33px
      height 33px
      line-height 34px
      z-index 0
      display flex
      align-items center
      justify-content center
      transition .3s ease-out .2s
      background var(--anzhiyu-white)
      border-radius 50%
  
  .description
    width 100%
    opacity 0
    transition .3s
    color var(--anzhiyu-white)
    display flex
    flex-direction column
    line-height 1.5
    gap 10px

  .bottom-group
    position absolute
    left 0
    bottom 0
    width 100%
    padding 1rem
    display flex
    justify-content space-between
    
    .left
      flex 1

      .name
        font-weight 700
        color var(--anzhiyu-white)
        font-size 20px
        line-height 1
        margin-bottom 5px

      .desc
        font-size 12px
        color var(--anzhiyu-white)
        opacity .6
        line-height 1
    
    .social-icons
      flex 1
      display flex
      justify-content flex-end
      gap 5px

      .social-icon
        color var(--anzhiyu-fontcolor)
        cursor pointer
        display flex
        transition all .3s

        &:hover
          transform scale(1.1)
          i
            color var(--anzhiyu-main)
            background var(--anzhiyu-white)

        i
          background var(--anzhiyu-white-op)
          color var(--anzhiyu-white)
          font-size 1rem
          width 40px
          height 40px
          display flex
          align-items center
          justify-content center
          transition all .3s ease 0s
          padding 8px
          border-radius 32px
```

---

## ⚙️ 步骤四：修改配置文件

### 文件：`_config.anzhiyu.yml`

在配置文件中找到 `aside.card_author` 部分，添加新的配置项：

```yaml
aside:
  card_author:
    enable: true
    description: 只有迎风，风筝才能飞得更高。 # 悬停时显示的描述
    sayhi: 你好！ # 顶部问候语
    subtitle: 分享网络安全与科技生活 # 底部副标题
    name_link: /about/ # 点击名字的链接
```

---

## 🧪 步骤五：测试和验证

完成修改后，进行测试：

```bash
# 清理缓存并重新生成
hexo clean && hexo g && hexo s
```

### 验证清单：

- [ ] 作者卡片是否显示渐变背景
- [ ] 悬停时是否显示描述文字
- [ ] 头像是否正确显示
- [ ] 贴纸/状态图标是否正确显示
- [ ] 社交图标是否正常工作
- [ ] 响应式设计是否正常
- [ ] 暗色模式下是否正常显示

---

## 🔧 故障排除

### 常见问题：

1. **样式不生效**
   - 检查 CSS 语法是否正确
   - 确认文件路径是否正确
   - 清理浏览器缓存

2. **图标不显示**
   - 检查图标类名是否正确
   - 确认图标字体是否加载

3. **悬停效果不工作**
   - 检查 JavaScript 是否有冲突
   - 确认 CSS 选择器是否正确

### 回滚方法：

如果出现问题，可以快速回滚：

```bash
# 恢复备份文件
cp backup/author-card-migration/card_author.pug themes/anzhiyu/layout/includes/widget/
cp backup/author-card-migration/aside.styl themes/anzhiyu/source/css/_layout/
cp backup/author-card-migration/_config.anzhiyu.yml ./
```

---

## 📝 注意事项

1. **兼容性**：确保修改后的样式与主题的其他部分兼容
2. **响应式**：测试在不同屏幕尺寸下的显示效果
3. **性能**：注意 CSS 动画对性能的影响
4. **可访问性**：确保颜色对比度符合可访问性标准

---

## 🎉 完成

完成所有步骤后，您的 AnZhiYu 主题将拥有与 Solitude 主题相似的现代化作者卡片设计！

如果在迁移过程中遇到任何问题，请参考故障排除部分或回滚到备份版本。
